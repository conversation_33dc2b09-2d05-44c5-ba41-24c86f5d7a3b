using JangAaina.Models;
using JangAaina.Data;
using Microsoft.EntityFrameworkCore;
using Dapper;
using JangAaina.DTO;

namespace JangAaina.Services;

public class DashboardService
{
    private readonly IApplicationDbContextFactory _dbContextFactory;

    public DashboardService(IApplicationDbContextFactory dbContextFactory)
    {
        _dbContextFactory = dbContextFactory;
    }
    public Task<int> GetNumberOfPublicationsAsync()
    {
        using var dc = _dbContextFactory.CreateDbContext();
        var factAdvertisingData = dc.Set<FactAdvertising>().ToList();
        return Task.FromResult(factAdvertisingData.Select(f => f.PublicationId).Distinct().Count());
    }

    public Task<(string StartDate, string EndDate)> GetAdvertisementPeriodAsync()
    {
        using var dc = _dbContextFactory.CreateDbContext();
        // Corrected the code to handle nullable DateTime instead of DateOnly
        var dates = dc.RawData
            //.Where(f => f.Date != null) // Check for non-null values
            .Select(f => f.Date.ToDateTime(TimeOnly.MinValue)) // Convert DateOnly to DateTime
            .Distinct()
            .ToList();

        if (dates.Any())
        {
            return Task.FromResult((dates.Min().ToString("d-MMM-yyyy"), dates.Max().ToString("d-MMM-yyyy")));
        }
        return Task.FromResult((string.Empty, string.Empty));
    }

    public Task<List<GovAdTypeRevenueDto>> GetGovAdTypeRevenueShareAsync()
    {
        using var dc = _dbContextFactory.CreateDbContext();
        var connection = dc.Database.GetDbConnection();
        var data = connection.Query<GovAdTypeRevenueDto>("dbo.dashboard_GovAdTypeWiseRevenue",
            commandType: System.Data.CommandType.StoredProcedure);

        var result = data.ToList();
        var totalRevenue = result.Sum(x => x.Revenue);

        foreach(var item in result)
        {
            item.Share = Math.Round((double)(item.Revenue / totalRevenue) * 100, 2);
        }

        return Task.FromResult(result);
    }

    public Task<List<DateOnly>> GetMissingDatesAsync(DateOnly startDate, DateOnly endDate)
    {
        using var dc = _dbContextFactory.CreateDbContext();
        // Get all dates that have data in RawData table
        var existingDates = dc.RawData
            //.Where(r => r.Date.HasValue)
            .Select(r => r.Date) // Ensure nullable value is accessed safely
            .Select(date => DateOnly.FromDateTime(date.ToDateTime(TimeOnly.MinValue))) // Convert DateTime to DateOnly
            .ToList();

        // Filter dates within the range
        existingDates = existingDates
            .Where(date => date >= startDate && date <= endDate)
            .Distinct()
            .ToList();

        // Generate all dates in the range
        var allDates = new List<DateOnly>();
        for (var date = startDate; date <= endDate; date = date.AddDays(1))
        {
            allDates.Add(date);
        }

        // Find missing dates
        return Task.FromResult( allDates
            .Where(d => !existingDates.Contains(d))
            .OrderBy(d => d)
            .ToList());
    }
}
