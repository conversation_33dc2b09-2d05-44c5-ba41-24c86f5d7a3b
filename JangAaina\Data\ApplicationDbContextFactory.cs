using JangAaina.Models;
using Microsoft.EntityFrameworkCore;

namespace JangAaina.Data;

/// <summary>
/// Factory implementation for creating ApplicationDbContext instances
/// </summary>
public class ApplicationDbContextFactory : IApplicationDbContextFactory
{
    private readonly DbContextOptions<ApplicationDbcontext> _options;

    public ApplicationDbContextFactory(DbContextOptions<ApplicationDbcontext> options)
    {
        _options = options;
    }

    /// <summary>
    /// Creates a new instance of ApplicationDbContext with the configured options
    /// </summary>
    /// <returns>A new ApplicationDbContext instance</returns>
    public ApplicationDbcontext CreateDbContext()
    {
        return new ApplicationDbcontext(_options);
    }
}
