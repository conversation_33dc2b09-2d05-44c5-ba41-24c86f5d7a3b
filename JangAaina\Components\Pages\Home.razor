﻿@page "/"
@using JangAaina.Services
@inject DashboardService dashboardService

<PageTitle>Jang Aaina - Home</PageTitle>

<link href="css/kpi-cards.css" rel="stylesheet" />

@*<div class="kpi-cards">
    <div class="kpi-card">
        <h3>Number of Publications</h3>
        <p>@numPublications</p>
    </div>
    <div class="kpi-card">
        <h3>Advertisement Period</h3>
        <p>@startDate <br/> @endDate</p>
    </div>
    <div class="kpi-card">
        <h3>Revenue Share</h3>
        <ul>
            @foreach (var item in govAdTypeRevenue)
            {
                <li>@item.GovAdTypeName: @item.Share%</li>
            }
        </ul>
    </div>
</div>*@

<!--<div class="missing-dates-section">
    <h3>Missing Data Dates</h3>
    <p>Dates with no data from August 1, 2024 to present:</p>
    <SfButton CssClass="e-primary" OnClick="GetMissingDate">Get Missing Dates</SfButton>
    <div class="missing-dates-grid">
        @foreach (var date in missingDates)
        {
            <div class="date-item">@date.ToString("d MMM yyyy")</div>
        }
    </div>-->
    @*@if (isLoading)
    {
        <div class="loading-indicator">Loading missing dates...</div>
    }
    else if (missingDates.Count == 0)
    {
        <p>No missing dates found in the selected period.</p>
    }*@
    
<!--</div>-->

<style>
    .missing-dates-section {
        margin-top: 2rem;
        padding: 1.5rem;
        background-color: #f8f9fa;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .missing-dates-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 10px;
        margin-top: 1rem;
    }
    
    .date-item {
        background-color: #e9ecef;
        padding: 8px 12px;
        border-radius: 4px;
        text-align: center;
        font-size: 0.9rem;
    }
    
    .loading-indicator {
        padding: 1rem;
        text-align: center;
        color: #6c757d;
    }
</style>

@code
{
    private int numPublications;
    private string startDate = string.Empty;
    private string endDate = string.Empty;
    private List<GovAdTypeRevenueDto> govAdTypeRevenue = new();
    private List<DateOnly> missingDates = new();
    private bool isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        //numPublications = await dashboardService.GetNumberOfPublicationsAsync();
        //(startDate, endDate) = await dashboardService.GetAdvertisementPeriodAsync();
        //govAdTypeRevenue = await dashboardService.GetGovAdTypeRevenueShareAsync();

        //await LoadMissingDates();
    }

    private async Task LoadMissingDates()
    {
        isLoading = true;



    }

    private async Task GetMissingDate(MouseEventArgs arg)
    {
        try
        {
            // Define date range
            DateOnly startDateToCheck = new DateOnly(2024, 8, 1);
            DateOnly endDateToCheck = DateOnly.FromDateTime(DateTime.Today);

            // Get missing dates using the dashboard service
            missingDates = await dashboardService.GetMissingDatesAsync(startDateToCheck, endDateToCheck);

        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading missing dates: {ex.Message}");
            missingDates = new List<DateOnly>();
        }


    }
}
