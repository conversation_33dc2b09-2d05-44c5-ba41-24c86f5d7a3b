using JangAaina.Data;
using JangAaina.DTO;
using JangAaina.Models;

namespace JangAaina.Services;

public class FOCRateCardService(IApplicationDbContextFactory dbContextFactory)
{
    public Task<List<FOCRateCardDto>> GetAllFOCRateCards(DateTime filterDate, int? editionId)
    {
        using var dc = dbContextFactory.CreateDbContext();
        var q = (from a in dc.FocrateCards
            orderby a.IsActive descending, a.Title
            where a.DateFrom <= filterDate
                  && filterDate <= a.DateTo
                  && (editionId == null || a.FocrateCardEditions.Any(m => m.EditionId == editionId))
            select new FOCRateCardDto
            {
                Id = a.Id,
                Title = a.Title,
                DateFrom = a.DateFrom,
                DateTo = a.DateTo,
                IsActive = a.IsActive,
                Priority = a.Priority,
                AllEditions = a.AllEditions,
                AllGovCategories = a.AllGovCategories,
                AllGovAdType = a.AllGovAdType,
                DiscountPer = a.DiscountPer,
                EditionStr = a.AllEditions
                    ? "All Editions"
                    : string.Join(", ", a.FocrateCardEditions.Select(k => k.Edition.EditionName).ToList()),
                GovCategoryStr = a.AllGovCategories
                    ? "All Categories"
                    : string.Join(", ", a.FocrateCardGovCategories.Select(k => k.GovCategory.GovCategoryName).ToList()),
                GovAdTypeStr = a.AllGovAdType
                    ? "All Ad Types"
                    : string.Join(", ", a.FocrateCardGovAdTypes.Select(k => k.GovAdType.GovAdTypeName).ToList())
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<EditionDto>> GetAllEditions()
    {
        using var dc = dbContextFactory.CreateDbContext();
        var q = (from a in dc.DimEditions
            orderby a.EditionName
            select new EditionDto
            {
                Id = a.EditionId,
                Title = a.EditionName
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<ClientCategoryDto>> GetAllGovCategories()
    {
        using var dc = dbContextFactory.CreateDbContext();
        var q = (from a in dc.DimGovCategories
            orderby a.GovCategoryName
            select new ClientCategoryDto
            {
                Id = a.GovCategoryId,
                Title = a.GovCategoryName
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<AdTypeCategoryDto>> GetAllGovAdType()
    {
        using var dc = dbContextFactory.CreateDbContext();
        var q = (from a in dc.DimGovAdTypes
            orderby a.GovAdTypeName
            select new AdTypeCategoryDto
            {
                Id = a.GovAdTypeId,
                Title = a.GovAdTypeName
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<string> SaveFOCRateCard(FOCRateCardDto rc, string user)
    {
        using var dc = dbContextFactory.CreateDbContext();
        var tran = dc.Database.BeginTransaction();
        try
        {
            var cardId = rc.Id;
            if (rc.Id == 0)
            {
                var card = new FocrateCard
                {
                    Title = rc.Title,
                    Id = 0,
                    IsActive = rc.IsActive,
                    AllEditions = rc.AllEditions,
                    AllGovCategories = rc.AllGovCategories,
                    AllGovAdType = rc.AllGovAdType,
                    DiscountPer = rc.DiscountPer,
                    CreatedBy = user,
                    CreatedDate = DateTime.Now,
                    DateFrom = rc.DateFrom,
                    DateTo = rc.DateTo,
                    Priority = rc.Priority ?? 0,
                    ModifiedBy = user,
                    ModifiedDate = DateTime.Now
                };
                dc.FocrateCards.Add(card);
                dc.SaveChanges();
                cardId = card.Id;
            }
            else
            {
                var card = (from a in dc.FocrateCards
                    where a.Id == rc.Id
                    select a).FirstOrDefault();
                if (card == null) return Task.FromResult("Record not found");

                card.Title = rc.Title;
                card.IsActive = rc.IsActive;
                card.AllEditions = rc.AllEditions;
                card.AllGovCategories = rc.AllGovCategories;
                card.AllGovAdType = rc.AllGovAdType;
                card.DiscountPer = rc.DiscountPer;
                card.ModifiedBy = user;
                card.ModifiedDate = DateTime.Now;
                card.DateFrom = rc.DateFrom;
                card.DateTo = rc.DateTo;
                card.Priority = rc.Priority ?? 0;
                dc.SaveChanges();
            }

            // Delete existing relations
            var existingEditions = dc.FocrateCardEditions.Where(x => x.FocrateCardId == cardId);
            dc.FocrateCardEditions.RemoveRange(existingEditions);

            var existingGovCategories = dc.FocrateCardGovCategories.Where(x => x.FocrateCardId == cardId);
            dc.FocrateCardGovCategories.RemoveRange(existingGovCategories);

            var existingGovAdTypes = dc.FocrateCardGovAdTypes.Where(x => x.FocrateCardId == cardId);
            dc.FocrateCardGovAdTypes.RemoveRange(existingGovAdTypes);

            var existingSubPublications = dc.FocrateCardSubPublications.Where(x => x.FocrateCardId == cardId).ToList();
            dc.FocrateCardSubPublications.RemoveRange(existingSubPublications);


            dc.SaveChanges();

            // Add new relations
            if (!rc.AllEditions && rc.SelectedEditionList?.Any() == true)
                foreach (var edition in rc.SelectedEditionList)
                    dc.FocrateCardEditions.Add(new FocrateCardEdition
                    {
                        FocrateCardId = cardId,
                        EditionId = edition.Id,
                        CreatedDate = DateTime.Now
                    });

            if (!rc.AllGovCategories && rc.SelectedGovCategoryList?.Any() == true)
                foreach (var category in rc.SelectedGovCategoryList)
                    dc.FocrateCardGovCategories.Add(new FocrateCardGovCategory
                    {
                        FocrateCardId = cardId,
                        GovCategoryId = category.Id,
                        CreateDate = DateTime.Now
                    });

            if (!rc.AllGovAdType && rc.SelectedGovAdTypes?.Any() == true)
                foreach (var adType in rc.SelectedGovAdTypes)
                    dc.FocrateCardGovAdTypes.Add(new FocrateCardGovAdType
                    {
                        FocrateCardId = cardId,
                        GovAdTypeId = adType.Id,
                        CreateDate = DateTime.Now
                    });

            foreach (var sp in rc.SelectedSubPublications)
                dc.FocrateCardSubPublications.Add(new FocrateCardSubPublication
                {
                    SubPublicationId = sp.Id, FocrateCardId = rc.Id,
                    CreatedDate = DateTime.Now
                });

            dc.SaveChanges();
            tran.Commit();
            return Task.FromResult("OK");
        }
        catch (Exception ex)
        {
            tran.Rollback();
            return Task.FromResult(ex.Message);
        }
    }

    public Task<FOCRateCardDto> OpenFOCRateCard(int id)
    {
        using var dc = dbContextFactory.CreateDbContext();
        var q = (from a in dc.FocrateCards
            where a.Id == id
            select new FOCRateCardDto
            {
                Id = a.Id,
                Title = a.Title,
                DateFrom = a.DateFrom,
                DateTo = a.DateTo,
                Priority = a.Priority,
                IsActive = a.IsActive,
                AllEditions = a.AllEditions,
                AllGovCategories = a.AllGovCategories,
                AllGovAdType = a.AllGovAdType,
                DiscountPer = a.DiscountPer
            }).First();

        q.SelectedEditionList = (from a in dc.FocrateCardEditions
            where a.FocrateCardId == id
            select new EditionDto
            {
                Id = a.EditionId,
                Title = a.Edition.EditionName
            }).ToList();

        q.SelectedGovCategoryList = (from a in dc.FocrateCardGovCategories
            where a.FocrateCardId == id
            select new ClientCategoryDto
            {
                Id = a.GovCategoryId,
                Title = a.GovCategory.GovCategoryName
            }).ToList();

        q.SelectedGovAdTypes = (from a in dc.FocrateCardGovAdTypes
            where a.FocrateCardId == id
            select new AdTypeCategoryDto
            {
                Id = a.GovAdTypeId,
                Title = a.GovAdType.GovAdTypeName
            }).ToList();
        q.SelectedSubPublications = (from a in dc.FocrateCardSubPublications
            where a.FocrateCardId == id
            select new SubPublicationDto
            {
                Id = a.SubPublicationId,
                Title = a.SubPublication.SubPublicationName
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<string> DeleteFOCRateCard(int id)
    {
        using var dc = dbContextFactory.CreateDbContext();
        try
        {
            var card = dc.FocrateCards.Find(id);
            if (card == null) return Task.FromResult("Record not found");

            // Remove related records
            var editions = dc.FocrateCardEditions.Where(x => x.FocrateCardId == id);
            dc.FocrateCardEditions.RemoveRange(editions);

            var govCategories = dc.FocrateCardGovCategories.Where(x => x.FocrateCardId == id);
            dc.FocrateCardGovCategories.RemoveRange(govCategories);

            var govAdTypes = dc.FocrateCardGovAdTypes.Where(x => x.FocrateCardId == id);
            dc.FocrateCardGovAdTypes.RemoveRange(govAdTypes);

            dc.FocrateCards.Remove(card);
            dc.SaveChanges();

            return Task.FromResult("OK");
        }
        catch (Exception ex)
        {
            return Task.FromResult(ex.Message);
        }
    }

    public Task<List<SubPublicationDto>> GetAllSubPublications()
    {
        using var dc = dbContextFactory.CreateDbContext();
        var q = (from a in dc.DimSubPublications
            orderby a.SubPublicationName
            select new SubPublicationDto
            {
                Id = a.SubPublicationId, Title = a.SubPublicationName
            }).ToList();
        return Task.FromResult(q);
    }
}