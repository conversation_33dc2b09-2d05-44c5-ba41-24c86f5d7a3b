﻿@page "/setup/ratecards"
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@inject RateCardService service
@rendermode InteractiveServer
@inject IJSRuntime js
@attribute [Authorize]
<style>
    /* Rate Card Table Styles */
    .RateCardTable {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 1rem;
        font-size: 0.7rem;
    }

    .RateCardTable th,
    .RateCardTable td {
        padding: 0.45rem;
        vertical-align: top;
        border-top: 1px solid #dee2e6;
        text-align: left;
    }

    .RateCardTable thead th {
        vertical-align: bottom;
        border-bottom: 2px solid #dee2e6;
        background-color: #f8f9fa;
        font-weight: 500;
        white-space: nowrap;
    }

    .RateCardTable tbody + tbody {
        border-top: 2px solid #dee2e6;
    }

    .RateCardTable.table-bordered { /* Use compound selector */
        border: 1px solid #dee2e6;
    }

    .RateCardTable.table-bordered th, /* Use compound selector */
    .RateCardTable.table-bordered td {
        border: 1px solid #dee2e6;
    }

    .RateCardTable.table-bordered thead th, /* Use compound selector */
    .RateCardTable.table-bordered thead td {
        border-bottom-width: 2px;
    }

    .RateCardTable.table-hover tbody tr:hover { /* Use compound selector */
        background-color: rgba(0, 0, 0, 0.075);
        cursor: pointer;
    }

    /* Sortable Column Styles (now targeting the table) */
    .RateCardTable th[style*="cursor: pointer;"] {
        user-select: none;
    }

    .RateCardTable th[style*="cursor: pointer;"]:hover {
        background-color: #e9ecef;
    }

    /* Sort Icon Styles (now targeting the table) */
    .RateCardTable th[style*="cursor: pointer;"].ascending:after {
        content: '\25B2';
    }

    .RateCardTable th[style*="cursor: pointer;"].descending:after {
        content: '\25BC';
    }

    /* Optional: Striped rows (now targeting the table) */
    .RateCardTable.table-striped tbody tr:nth-of-type(odd) {
        background-color: rgba(0, 0, 0, 0.05);
    }
    /*Responsive adjustments (optional) */
    @@media (max-width: 768px) {
    .RateCardTable {
        font-size: 0.8rem; /* Further reduce font on smaller screens */
    }

    /* Example of making the table horizontally scrollable */
    .table-responsive {
        overflow-x: auto;
    }
    }
 .e-grid.e-responsive .e-rowcell, .e-grid.e-responsive .e-headercelldiv
 {
     font-size: 12px !important;
     padding: 3px 5px !important;
 }
</style>
<div style="display:flex; gap:10px; align-items:center;">
    <h3>Rate Cards </h3>
    <SfButton CssClass="e-primary" OnClick="OpenCreateForm">Create</SfButton>
</div>

<div class="row mb-2">
    <div class="col-2">
        <SfDatePicker @bind-Value="FilterDate" FloatLabelType="FloatLabelType.Always" Placeholder="Filter Date" Format="d MMM, yyyy"></SfDatePicker>
    </div>
    <div class="col-md">
        <SfDropDownList TValue="int?" TItem="SubPublicationDto" DataSource="SubPublicationList" Placeholder="Publication" AllowFiltering="true" FilterType="FilterType.Contains" @bind-Value="SubPublicationId" ShowClearButton="true" FloatLabelType="FloatLabelType.Always">
            <DropDownListFieldSettings Value="Id" Text="Title"></DropDownListFieldSettings>
        </SfDropDownList>
    </div>
    <div class="col-md">
        <SfDropDownList TValue="int?" TItem="EditionDto" DataSource="EditionList" Placeholder="Edition" AllowFiltering="true" FilterType="FilterType.Contains" @bind-Value="EditionId" ShowClearButton="true" FloatLabelType="FloatLabelType.Always">
            <DropDownListFieldSettings Value="Id" Text="Title"></DropDownListFieldSettings>
        </SfDropDownList>
    </div>
    <div class="col-md-1">
        <p style="height: 7px;"></p>
        <SfButton CssClass="e-info" OnClick="DoSearch">Search</SfButton>
    </div>
</div>

<div class="row">
    <div class="col">
        @if (RateCardList.Any())
        {
            <SfGrid DataSource="RateCardList" AllowSorting="true" AllowFiltering="true" Height="calc(100vh - 235px)">
                <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn Field="Title" HeaderText="Title" TextAlign="TextAlign.Left" AutoFit="true">
                        <Template Context="context">
                            @{
                                var rateCard = (RateCardDto)context;
                                <a href="/setup/ratecards/@rateCard.Id/edit" target="_blank">@rateCard.Title</a>
                            }
                        </Template>
                    </GridColumn>
                    <GridColumn Field="SubPublicationsStr" HeaderText="Publications" TextAlign="TextAlign.Left" AutoFit="true"></GridColumn>
                    <GridColumn Field="DateFrom" HeaderText="Date From" Format="d MMM, yyyy" TextAlign="TextAlign.Left" AutoFit="true"></GridColumn>
                    <GridColumn Field="DateTo" HeaderText="Date To" Format="d MMM, yyyy" TextAlign="TextAlign.Left" AutoFit="true"></GridColumn>
                    <GridColumn Field="Status" HeaderText="Status" TextAlign="TextAlign.Left" AutoFit="true"></GridColumn>
                    <GridColumn Field="EditionStr" HeaderText="Editions" TextAlign="TextAlign.Left" AutoFit="true"></GridColumn>
                    <GridColumn Field="CitiesStr" HeaderText="Cities" TextAlign="TextAlign.Left" AutoFit="true"></GridColumn>
                    <GridColumn Field="GovCategoryStr" HeaderText="Category" TextAlign="TextAlign.Left" AutoFit="true"></GridColumn>
                    <GridColumn Field="GovAdTypeStr" HeaderText="Ad Category" TextAlign="TextAlign.Left" AutoFit="true"></GridColumn>
                    <GridColumn Field="Priority" HeaderText="Priority" TextAlign="TextAlign.Left" AutoFit="true"></GridColumn>
                    <GridColumn HeaderText="Actions" TextAlign="TextAlign.Center" AutoFit="true">
                        <Template Context="context">
                            @{
                                var rateCard = (RateCardDto)context;
                                <SfButton OnClick="@(() => DeleteRateCard(rateCard.Id))" CssClass="e-danger">Delete</SfButton>
                            }
                        </Template>
                    </GridColumn>
                </GridColumns>
            </SfGrid>
        }
        else
        {
            <p>No rate cards found.</p>
        }
    </div>
</div>

@code {

    private string CurrentSortColumn { get; set; }
    private bool IsAscendingSort { get; set; } = true;
    public required DateTime FilterDate = DateTime.Today;
    public List<SubPublicationDto> SubPublicationList = new();
    private int? SubPublicationId { get; set; }
    private int? EditionId { get; set; }
    private List<EditionDto> EditionList = new();
    private List<RateCardDto> RateCardList = new();

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        SubPublicationList = await service.GetAllSubPublications();
        EditionList = await service.GetAllEditions();
        RateCardList = await service.GetAllRateCards(FilterDate, SubPublicationId, EditionId);
    }

    private void OpenCreateForm() => NavMgr.NavigateTo("/setup/ratecards/create");

    private async Task DoSearch() => RateCardList = await service.GetAllRateCards(FilterDate, SubPublicationId, EditionId);

    private void OpenEditForm(int id) => NavMgr.NavigateTo($"/setup/ratecards/{id}/edit");

    private async Task DeleteRateCard(int id)
    {
        var confirm = await js.InvokeAsync<bool>("confirm", "Are you sure you want to delete this Rate Card?");
        if (!confirm) return;

        var result = await service.DeleteRateCard(id);
        if (result == "OK") await DoSearch();
    }

    private void SortBy(string columnName)
    {
        if (CurrentSortColumn == columnName)
        {
            IsAscendingSort = !IsAscendingSort;
        }
        else
        {
            CurrentSortColumn = columnName;
            IsAscendingSort = true;
        }

        switch (columnName)
        {
            case "Title":
                RateCardList = IsAscendingSort ? RateCardList.OrderBy(x => x.Title).ToList() : RateCardList.OrderByDescending(x => x.Title).ToList();
                break;
            case "SubPublicationsStr":
                RateCardList = IsAscendingSort ? RateCardList.OrderBy(x => x.SubPublicationsStr).ToList() : RateCardList.OrderByDescending(x => x.SubPublicationsStr).ToList();
                break;
            case "DateFrom":
                RateCardList = IsAscendingSort ? RateCardList.OrderBy(x => x.DateFrom).ToList() : RateCardList.OrderByDescending(x => x.DateFrom).ToList();
                break;
            case "DateTo":
                RateCardList = IsAscendingSort ? RateCardList.OrderBy(x => x.DateTo).ToList() : RateCardList.OrderByDescending(x => x.DateTo).ToList();
                break;
            case "Status":
                RateCardList = IsAscendingSort ? RateCardList.OrderBy(x => x.Status).ToList() : RateCardList.OrderByDescending(x => x.Status).ToList();
                break;
            case "EditionStr":
                RateCardList = IsAscendingSort ? RateCardList.OrderBy(x => x.EditionStr).ToList() : RateCardList.OrderByDescending(x => x.EditionStr).ToList();
                break;
            case "CitiesStr":
                RateCardList = IsAscendingSort ? RateCardList.OrderBy(x => x.CitiesStr).ToList() : RateCardList.OrderByDescending(x => x.CitiesStr).ToList();
                break;
            case "Priority":
                RateCardList = IsAscendingSort ? RateCardList.OrderBy(x => x.Priority).ToList() : RateCardList.OrderByDescending(x => x.Priority).ToList();
                break;
        }
    }
    private string GetSortIcon(string columnName)
    {
        if (CurrentSortColumn != columnName)
        {
            return string.Empty;
        }

        return IsAscendingSort ? "▲" : "▼";
    }
}
