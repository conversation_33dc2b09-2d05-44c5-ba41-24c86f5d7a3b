{"$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:47450", "sslPort": 44385}}, "profiles": {"http": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "applicationUrl": "http://localhost:5165", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "windowsAuthentication": true, "anonymousAuthentication": false}, "https": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "applicationUrl": "https://localhost:7179;http://localhost:5165", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "windowsAuthentication": true, "anonymousAuthentication": false}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "windowsAuthentication": true, "anonymousAuthentication": false}}}