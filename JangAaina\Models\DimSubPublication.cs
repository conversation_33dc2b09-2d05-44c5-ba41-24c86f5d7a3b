﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace JangAaina.Models;

public partial class DimSubPublication
{
    public int SubPublicationId { get; set; }

    public string SubPublicationName { get; set; } = null!;

    public virtual ICollection<FactAdvertising> FactAdvertisings { get; set; } = new List<FactAdvertising>();

    public virtual ICollection<FocrateCardSubPublication> FocrateCardSubPublications { get; set; } = new List<FocrateCardSubPublication>();

    public virtual ICollection<RateCardSubPublication> RateCardSubPublications { get; set; } = new List<RateCardSubPublication>();
}