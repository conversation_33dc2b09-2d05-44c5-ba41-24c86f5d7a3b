﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace JangAaina.Models;

public partial class DimEdition
{
    public int EditionId { get; set; }

    public string EditionName { get; set; } = null!;

    public DateTime CreatedDate { get; set; }

    public virtual ICollection<FocrateCardEdition> FocrateCardEditions { get; set; } = new List<FocrateCardEdition>();

    public virtual ICollection<RateCardEdition> RateCardEditions { get; set; } = new List<RateCardEdition>();
}