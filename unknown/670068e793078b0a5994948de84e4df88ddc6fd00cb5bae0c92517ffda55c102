using Microsoft.AspNetCore.Components.Authorization;
using System.Security.Claims;

namespace JangAaina.Services;

public class ServerAuthenticationStateProvider : AuthenticationStateProvider
{
    public override Task<AuthenticationState> GetAuthenticationStateAsync()
    {
        var identity = new ClaimsIdentity(new[]
        {
            new Claim(ClaimTypes.Name, "<EMAIL>"),
        }, "ServerAuth");

        var user = new ClaimsPrincipal(identity);

        return Task.FromResult(new AuthenticationState(user));
    }
}