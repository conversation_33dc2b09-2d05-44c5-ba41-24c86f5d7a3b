﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace JangAaina.Models;

public partial class RateCard
{
    public int Id { get; set; }

    public string Title { get; set; } = null!;

    public DateTime DateFrom { get; set; }

    public DateTime DateTo { get; set; }

    public string? Description { get; set; }

    public string CreatedBy { get; set; } = null!;

    public DateTime CreatedDate { get; set; }

    public string? ModifiedBy { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public int Priority { get; set; }

    public bool AllAdTypes { get; set; }

    public bool AllCategories { get; set; }

    public bool AllIndustries { get; set; }

    public bool AllClients { get; set; }

    public bool IsActive { get; set; }

    public bool AllEditions { get; set; }

    public decimal PremiumPercentage { get; set; }

    public bool AllDays { get; set; }

    public bool AllGovCategories { get; set; }

    public bool AllGovAdTypes { get; set; }

    public bool AllCities { get; set; }

    public bool IsDeleted { get; set; }

    public virtual ICollection<RateCardAdType> RateCardAdTypes { get; set; } = new List<RateCardAdType>();

    public virtual ICollection<RateCardCategory> RateCardCategories { get; set; } = new List<RateCardCategory>();

    public virtual ICollection<RateCardCity> RateCardCities { get; set; } = new List<RateCardCity>();

    public virtual ICollection<RateCardClient> RateCardClients { get; set; } = new List<RateCardClient>();

    public virtual ICollection<RateCardEdition> RateCardEditions { get; set; } = new List<RateCardEdition>();

    public virtual ICollection<RateCardGovAdType> RateCardGovAdTypes { get; set; } = new List<RateCardGovAdType>();

    public virtual ICollection<RateCardGovCategory> RateCardGovCategories { get; set; } = new List<RateCardGovCategory>();

    public virtual ICollection<RateCardIndustry> RateCardIndustries { get; set; } = new List<RateCardIndustry>();

    public virtual ICollection<RateCardPageAndColor> RateCardPageAndColors { get; set; } = new List<RateCardPageAndColor>();

    public virtual ICollection<RateCardSubPublication> RateCardSubPublications { get; set; } = new List<RateCardSubPublication>();
}