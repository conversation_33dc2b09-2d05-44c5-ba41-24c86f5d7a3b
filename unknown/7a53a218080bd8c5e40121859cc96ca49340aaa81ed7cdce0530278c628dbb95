@using System.Security.Claims
@using Microsoft.AspNetCore.Components.Authorization
@inject NavigationManager Navigation

<AuthorizeView>
    <Authorized>
        <div class="user-info">
            <span>Welcome @context.User.Identity?.Name</span>
            @* @if (context.User.Identity?.IsAuthenticated == true)
            {
                <span>(@context.User.FindFirst(ClaimTypes.Role)?.Value)</span>
            } *@
        </div>
    </Authorized>
    <NotAuthorized>
        <span>Not authenticated</span>
    </NotAuthorized>
</AuthorizeView>