.import-container {
    padding: 2rem;
    background-color: var(--neutral-layer-1);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.import-header {
    margin-bottom: 2rem;
}

.import-header h2 {
    color: var(--neutral-foreground-rest);
    font-size: 1.75rem;
    margin-bottom: 0.5rem;
}

.import-header p {
    color: var(--neutral-foreground-hint);
    margin-bottom: 0;
}

.file-upload-zone {
    border: 2px dashed var(--neutral-stroke-rest);
    border-radius: 6px;
    padding: 2rem;
    text-align: center;
    background-color: var(--neutral-layer-2);
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-zone:hover {
    border-color: var(--accent-fill-rest);
    background-color: var(--neutral-layer-3);
}

.dates-preview {
    background-color: var(--neutral-layer-2);
    border-radius: 6px;
    padding: 1.5rem;
    margin-top: 2rem;
}

.dates-preview h5 {
    color: var(--neutral-foreground-rest);
    margin-bottom: 1rem;
}

.dates-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.date-chip {
    background-color: var(--accent-fill-rest);
    color: var(--neutral-foreground-on-accent);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
}

.warning-alert {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 6px;
    background-color: var(--warning-fill-rest);
    color: var(--warning-foreground-rest);
    font-size: 0.875rem;
}

.grid-container {
    margin-top: 2rem;
    background-color: var(--neutral-layer-2);
    border-radius: 6px;
    padding: 1.5rem;
}