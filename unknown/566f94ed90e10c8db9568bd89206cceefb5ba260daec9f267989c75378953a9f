﻿@using Microsoft.AspNetCore.Components.Authorization
@using Orientation = Microsoft.FluentUI.AspNetCore.Components.Orientation

@inherits LayoutComponentBase
@using MudBlazor

@* Required *@
<MudThemeProvider />
<MudPopoverProvider />

@* Needed for dialogs *@
<MudDialogProvider />

@* Needed for snackbars *@
<MudSnackbarProvider />

<FluentLayout>
    <FluentHeader>
        <div style="display: flex; justify-content: space-between; align-items: center; width: 100%; padding: 0 20px;">
            
            <AuthorizeView>
                <Authorized>
                    
                        <div><PERSON></div>
                        <div>@context.User.Identity.Name</div>
                    
                </Authorized>
                <NotAuthorized>
                    
                        <div>Jang Aaina - Not Authorized</div>
                        
                    
                </NotAuthorized>
            </AuthorizeView>
        </div>
    </FluentHeader>
    <FluentStack Class="main" Orientation="Orientation.Horizontal" Width="100%">
        <AuthorizeView>
            <Authorized>
                <NavMenu />
                <FluentBodyContent Class="body-content">
                    <div class="content">
                        @Body
                    </div>
                </FluentBodyContent>
            </Authorized>
            <NotAuthorized>
                <div class="container-xxl">
                    <p>You are not authorized to view this content. Please contact your administrator.</p>
                </div>
            </NotAuthorized>
        </AuthorizeView>
    </FluentStack>
</FluentLayout>

<div id="blazor-error-ui">
    An unhandled error has occurred.
    <a href="" class="reload">Reload</a>
    <a class="dismiss">🗙</a>
</div>

@code {
    private string UserName = "John Doe"; // Replace with actual logic to fetch the logged-in user's name
}
