﻿using System.ComponentModel.DataAnnotations;

namespace JangAaina.DTO;

#nullable disable
public class FOCRateCardDto
{
    public int Id { get; set; }
    [Required]
    [StringLength(200)]
    public string Title { get; set; }
    [Required]
    public DateTime DateFrom { get; set; }
    [Required]
    public DateTime DateTo { get; set; }
    [Required]
    public int? Priority { get; set; }
    public bool IsActive { get; set; }
    public string Status => IsActive ? "Active" : "Inactive";
    public decimal DiscountPer { get; set; }

    public List<EditionDto> SelectedEditionList { get; set; } = new();
    public bool AllEditions { get; set; } = true;
    public string EditionStr { get; set; }

    public bool AllGovCategories { get; set; } = true;
    public List<ClientCategoryDto> SelectedGovCategoryList { get; set; } = new();
    public string GovCategoryStr { get; set; }

    public bool AllGovAdType { get; set; } = true;
    public List<AdTypeCategoryDto> SelectedGovAdTypes { get; set; } = new();
    public string GovAdTypeStr { get; set; }
    public List<SubPublicationDto> SelectedSubPublications { get; set; }
}

#nullable disable
public class RateCardDto
{
    public string CitiesStr { get; set; }
    public int Id { get; set; }
    [Required]
    [StringLength(200)]
    public string Title { get; set; }
    public string SubPublicationsStr { get; set; }
    [Required]
    public DateTime DateFrom { get; set; }
    [Required]
    public DateTime DateTo { get; set; }
    public string SubPublications { get; set; }
    [Required]
    public int? Priority { get; set; }
    public bool IsActive { get; set; }
    public string Status => IsActive ? "Active" : "Inactive";
    [MinLength(1, ErrorMessage = "At least one sub-publication is required")]
    public List<SubPublicationDto> SelectedSubPublicationList { get; set; } = new();

    public List<EditionDto> SelectedEditionList { get; set; } = new();
    public List<DayDto> SelectedDaysList { get; set; } = new();
    public List<AdTypeDto> SelectedAdTypeList { get; set; } = new();

    public List<CategoryDto> SelectedCategoryList { get; set; } = new();
    public List<ClientDto> SelectedClientList { get; set; } = new();
    public List<IndustryDto> SelectedIndustryList { get; set; } = new();
    public List<RateCardDetailDto> Rates { get; set; } = new();
    public bool AllCategories { get; set; } = true;
    public bool AllIndustries { get; set; } = true;
    public bool AllClients { get; set; } = true;
    public bool AllAdTypes { get; set; } = true;
    public bool AllEditions { get; set; } = true;



    public string EditionStr { get; set; }
    public decimal PremiumPercentage { get; set; } = 0;
    public bool AllDays { get; set; } = true;
    public bool AllGovCategories { get; set; } = true;
    public List<ClientCategoryDto> SelectedGovCategoryList { get; set; }
    public bool AllGovAdTypes { get; set; } = true;
    public List<AdTypeCategoryDto> SelectedGovAdTypes { get; set; }
    public bool AllCities { get; set; } = true;
    public List<EditionDto> SelectedCitiesList { get; set; } = new();
    public string GovCategoryStr {get;set;}
    public string GovAdTypeStr { get;set;}
}


