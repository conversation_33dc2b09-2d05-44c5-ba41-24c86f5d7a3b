﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace JangAaina.Models;

public partial class DimIndustry
{
    public int IndustryId { get; set; }

    public string IndustryName { get; set; } = null!;

    public virtual ICollection<FactAdvertising> FactAdvertisings { get; set; } = new List<FactAdvertising>();

    public virtual ICollection<FocrateCardIndustry> FocrateCardIndustries { get; set; } = new List<FocrateCardIndustry>();

    public virtual ICollection<RateCardIndustry> RateCardIndustries { get; set; } = new List<RateCardIndustry>();
}