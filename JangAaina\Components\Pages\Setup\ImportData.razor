﻿@page "/setup/ratecards/import"
@using Color = MudBlazor.Color
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using Icons = MudBlazor.Icons
@inject RateCardService service
@rendermode InteractiveServer
@implements IAsyncDisposable
<h3>Import Data</h3>
<PageTitle>Import Data</PageTitle>
<SfToast @ref="sfToast"></SfToast>
<div class="row">
    <div class="col-md">
        <SfGrid DataSource="AllMonthsList" AllowFiltering="true" AllowSorting="true">
            <GridFilterSettings Type="FilterType.Excel"/>
            <GridColumns>
                <GridColumn HeaderText="Year" Field="Year" AutoFit="true"></GridColumn>
                <GridColumn HeaderText="Month" Field="Month" AutoFit="true"></GridColumn>
                <GridColumn HeaderText="Total Spots" Field="Spots" AutoFit="true"></GridColumn>
                <GridColumn HeaderText="Difference" Field="Difference" AutoFit="true"></GridColumn>
                <GridColumn HeaderText="Action" AutoFit="true">
                    <Template Context="mm">
                        @{
                            if (mm is ImportMonthDto { Difference: > 0 } obj)
                            {
                                
                                <MudButton OnClick="@(() => DoImport(obj.Year, obj.Month))" Size="Size.Small" Variant="Variant.Filled" Color="Color.Primary" StartIcon="@Icons.Material.Filled.ImportExport" >
                                    @if (_isProcessing)
                                    {
                                        <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                                        <MudText Class="ms-2">Processing</MudText>
                                    }
                                    else
                                    {
                                        <MudText>Import</MudText>
                                    }
                                </MudButton>
                                

                                
                                
                            }
                        }
                    </Template>
                </GridColumn>
            </GridColumns>
        </SfGrid>
    </div>
</div>

@code {
    private List<ImportMonthDto> AllMonthsList { get; set; } = new();
    private bool _disposed = false;

    protected override async Task OnInitializedAsync()
    {
        if (_disposed) return;

        await base.OnInitializedAsync();

        if (_disposed) return;

        AllMonthsList = await service.GetImportMonthList();
    }

    private SfToast sfToast;
    private bool _isProcessing = false;
    private async Task DoImport(int year, int month)
    {
        if (_disposed) return;

        _isProcessing = true;
        StateHasChanged();
        var res = await service.DoImport(year, month);

        if (_disposed) return;

        if (res != "OK")
        {
            var mm = new ToastModel { Content = res, Title = "Error", ShowProgressBar = true, Timeout = 5000, ShowCloseButton = true };
            if (sfToast != null && !_disposed)
                await sfToast.ShowAsync(mm);
        }
        else
        {
            if (!_disposed)
                AllMonthsList = await service.GetImportMonthList();
        }

        _isProcessing = false;
        if (!_disposed)
            StateHasChanged();
    }

    public async ValueTask DisposeAsync()
    {
        _disposed = true;

        // Give a small delay to allow any pending toast operations to complete
        await Task.Delay(100);

        // Dispose the toast component if it exists
        if (sfToast is IAsyncDisposable asyncDisposable)
        {
            await asyncDisposable.DisposeAsync();
        }
        else if (sfToast is IDisposable disposable)
        {
            disposable.Dispose();
        }
    }

}