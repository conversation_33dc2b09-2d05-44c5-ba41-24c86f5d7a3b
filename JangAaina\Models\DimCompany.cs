﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace JangAaina.Models;

public partial class DimCompany
{
    public int CompanyId { get; set; }

    public string CompanyName { get; set; } = null!;

    public virtual ICollection<FactAdvertising> FactAdvertisings { get; set; } = new List<FactAdvertising>();

    public virtual ICollection<FocrateCardClient> FocrateCardClients { get; set; } = new List<FocrateCardClient>();

    public virtual ICollection<RateCardClient> RateCardClients { get; set; } = new List<RateCardClient>();
}