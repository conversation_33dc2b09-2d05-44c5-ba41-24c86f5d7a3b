﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace JangAaina.Models;

public partial class RawDatum
{
    public DateOnly Date { get; set; }

    public string? Day { get; set; }

    public DateOnly? Month { get; set; }

    public DateOnly? Year { get; set; }

    public string? Country { get; set; }

    public string? City { get; set; }

    public string? Publication { get; set; }

    public string? SubPublication { get; set; }

    public string? Language { get; set; }

    public string? Industry { get; set; }

    public string? Category { get; set; }

    public string? Brand { get; set; }

    public string? Variant { get; set; }

    public string? Caption { get; set; }

    public string? Company { get; set; }

    public string? PageType { get; set; }

    public string? AdType { get; set; }

    public string? Colour { get; set; }

    public int? PageNo { get; set; }

    public int? Size1 { get; set; }

    public int? Size2 { get; set; }

    public double? Space { get; set; }

    public double? Cost { get; set; }

    public string? MediaAgency { get; set; }

    public string? PublishType { get; set; }

    public string? Frequency { get; set; }

    public string? GovCategory { get; set; }

    public string? GovAdType { get; set; }

    public Guid Id { get; set; }

    public string? Edition { get; set; }
}