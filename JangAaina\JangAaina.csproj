<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="Services\ServerAuthenticationStateProvider.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="efpt.config.json.user" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="ClosedXML" Version="0.105.0" />
    <PackageReference Include="Dapper" Version="2.1.66" />
    <PackageReference Include="EPPlus" Version="8.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Negotiate" Version="9.0.6" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
    <PackageReference Include="microsoft.entityframeworkcore" Version="9.0.6" />
    <PackageReference Include="microsoft.entityframeworkcore.design" Version="9.0.6">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="microsoft.entityframeworkcore.sqlserver" Version="9.0.6" />
    <PackageReference Include="microsoft.entityframeworkcore.tools" Version="9.0.6">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.FluentUI.AspNetCore.Components" Version="4.*-* " />
    <PackageReference Include="Microsoft.FluentUI.AspNetCore.Components.Icons" Version="4.*-* " />
    <PackageReference Include="mudblazor" Version="8.7.0" />
    <PackageReference Include="syncfusion.blazor" Version="29.2.10" />
    <PackageReference Include="syncfusion.blazor.themes" Version="29.2.10" />
    <PackageReference Include="EFCore.BulkExtensions" Version="9.0.1" />
  </ItemGroup>
</Project>
