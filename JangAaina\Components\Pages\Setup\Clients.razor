﻿@page "/setup/clients"
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using SelectionType = Syncfusion.Blazor.Grids.SelectionType
@inject RateCardService service
@rendermode InteractiveServer
<h3>Clients</h3>
<div class="row">
    <div class="col-md-3 mb-2">
        <SfDropDownList DataSource="@ClientCategoryList" AllowFiltering="true" FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains"
                        TItem="ClientCategoryDto" Placeholder="Select Category" FloatLabelType="FloatLabelType.Always"
                        @bind-Value="selectedClientCategory" TValue="int?">
            <DropDownListFieldSettings Text="@nameof(ClientCategoryDto.Title)" Value="@nameof(ClientCategoryDto.Id)"></DropDownListFieldSettings>
        </SfDropDownList>
    </div>
    <div class="col-md-1 mb-2 pt-4">
        <SfButton CssClass="e-primary" type="button" OnClick="ApplyClientCategory">Apply</SfButton>
    </div>
    <div class="col-md-3 mb-2">
        <SfDropDownList DataSource="@SubCategoryList" AllowFiltering="true" FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains"
                        TItem="SubCategoryDto" Placeholder="Select Sub Category" FloatLabelType="FloatLabelType.Always"
                        @bind-Value="selectedSubCategory" TValue="int?">
            <DropDownListFieldSettings Text="@nameof(SubCategoryDto.Name)" Value="@nameof(SubCategoryDto.Id)"></DropDownListFieldSettings>
        </SfDropDownList>
    </div>
    <div class="col-md-1 mb-2 pt-4">
        <SfButton CssClass="e-primary" type="button" OnClick="ApplySubCategory">Apply</SfButton>
    </div>
    <div class="col-md-3 mb-2">
        <SfDropDownList DataSource="@AdCategoryList" AllowFiltering="true" FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains"
                        TItem="SubCategoryDto" Placeholder="Select Ad Category" FloatLabelType="FloatLabelType.Always"
                        @bind-Value="selectedAdCategory" TValue="int?">
            <DropDownListFieldSettings Text="@nameof(SubCategoryDto.Name)" Value="@nameof(SubCategoryDto.Id)"></DropDownListFieldSettings>
        </SfDropDownList>
    </div>
    <div class="col-md-1 mb-2 pt-4">
        <SfButton CssClass="e-primary" type="button" OnClick="ApplyClientAdCategory">Apply</SfButton>
    </div>
</div>
<div class="row">
    <div class="col">
        <SfGrid DataSource="ClientList" AllowFiltering="true" AllowSorting="true" AllowPaging="true" AllowSelection="true" Height="calc(100vh - 310px)"
                @ref="@dgMain">

            <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
            <GridSelectionSettings Type="SelectionType.Multiple"></GridSelectionSettings>
            <GridColumns>
                @*<GridColumn HeaderText="" AutoFit="true">
                    
                    <Template Context="cc">
                    @{
                        if(cc is ClientDto mm)
                                                    {
                            <input type="checkbox" @bind="mm.Selected" />
                        }
                    }
                    </Template>
                </GridColumn>*@
                <GridColumn HeaderText="Client" AutoFit="true" Field="@nameof(ClientDto.Title)"></GridColumn>
                <GridColumn HeaderText="Client Category" Width="200px" Field="@nameof(ClientDto.ClientCategory)"></GridColumn>
                <GridColumn HeaderText="SubCategoy" AutoFit="true" Field="@nameof(ClientDto.SubCategory)"></GridColumn>
                <GridColumn HeaderText="Ad Type Category" AutoFit="true" Field="@nameof(ClientDto.AdTypeCategory)"></GridColumn>
                <GridColumn HeaderText="Industry" AutoFit="true" Field="@nameof(ClientDto.Industry)"></GridColumn>
            </GridColumns>
        </SfGrid>
    </div>

</div>

@code {
    private List<ClientDto> ClientList = new();
    private List<ClientCategoryDto> ClientCategoryList = new();
    private SfGrid<ClientDto> dgMain;
    public int? selectedClientCategory { get; set; }
    public List<SubCategoryDto> SubCategoryList { get; set; } = new();
    public int? selectedSubCategory { get; set; }
    public List<SubCategoryDto> AdCategoryList { get; set; } = new();
    public int? selectedAdCategory { get; set; }

    protected override async Task OnInitializedAsync()
    {
        ClientList = await service.GetClients();
        ClientCategoryList = await service.GetClientCategories();
        //SubCategoryList = await service.GetSubCategories();
        AdCategoryList = await service.GetAdCategories();
    }

    private async Task ApplyCategory(int clientCategoryId)
    {
        var q = ClientList.Where(x => x.Selected).ToList();
        if (q.Count > 0)
        {
            //await service.ApplyCategory(q, clientCategoryId);
            ClientList = await service.GetClients();
        }
    }

    private async Task ApplyClientCategory(MouseEventArgs obj)
    {
        if (dgMain != null)
        {
            var q = await dgMain.GetSelectedRecordsAsync();
            if (q is { Count: > 0 })
            {
                //await service.ApplyCategory(q, selectedClientCategory);
                ClientList = await service.GetClients();
            }
        }
    }

    private async Task ApplySubCategory(MouseEventArgs obj)
    {
        if (dgMain != null)
        {
            var q = await dgMain.GetSelectedRecordsAsync();
            if (q is { Count: > 0 })
            {
                await service.ApplySubCategory(q, selectedSubCategory);
                ClientList = await service.GetClients();
            }
        }
    }

    private async Task ApplyClientAdCategory(MouseEventArgs obj)
    {
        //throw new NotImplementedException();
        if (dgMain != null)
        {
            var q = await dgMain.GetSelectedRecordsAsync();
            if (q is { Count: > 0 })
            {
                await service.ApplyAdCategory(q, selectedAdCategory);
                ClientList = await service.GetClients();
            }
        }
    }

}