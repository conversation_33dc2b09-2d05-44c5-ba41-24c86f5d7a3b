﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace JangAaina.Models;

public partial class DimPublication
{
    public int PublicationId { get; set; }

    public string PublicationName { get; set; } = null!;

    public bool IsJungPublication { get; set; }

    public virtual ICollection<FactAdvertising> FactAdvertisings { get; set; } = new List<FactAdvertising>();
}